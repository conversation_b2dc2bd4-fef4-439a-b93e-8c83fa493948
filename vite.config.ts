// vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig(({ command }) => {
  if (command === 'serve') {
    return {
      plugins: [react()],
    }
  } else {
    // command === 'build'
    return {
      plugins: [react()],
      build: {
        lib: {
          entry: 'src/main.tsx',
          name: 'Widget',
          fileName: 'widget',
          formats: ['iife'],
        },
      },
      define: {
        'process.env.NODE_ENV': JSON.stringify('production'),
        'process.env': '{}',
      },
    }
  }
})
