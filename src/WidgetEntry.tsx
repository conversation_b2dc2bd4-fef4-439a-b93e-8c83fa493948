
import { useState, useEffect } from 'react';
import AiSummaryIcon from './components/icons/AiSummaryIcon';
import ForYouIcon from './components/icons/ForYouIcon';
import NewsDetailsDialog from './components/NewsDetailsDialog';
import NewsWidget from './components/NewsWidget';
// import ForYouIcon from '../components/icons/ForYouIcon';
// import NewsWidget from '../components/NewsWidget';
// import NewsDetailsDialog from '../components/NewsDetailsDialog';
// import AiSummaryIcon from '@/components/icons/AiSummaryIcon';

interface QAPair {
  question: string;
  answer: string;
}

interface RelatedPost {
  post_title: string;
  relevance_summary: string;
  qa_pairs: QAPair[];
  score: number;
  post_url: string;
}

interface NewsData {
  post_id: string;
  main_post_summary: string;
  related_posts: RelatedPost[];
}

const Index = () => {
  const [widgetMode, setWidgetMode] = useState<'summary' | 'related' | null>(null);
  const [newsData, setNewsData] = useState<NewsData | null>(null);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [shouldShowWidget, setShouldShowWidget] = useState(false);

  // State for details dialog
  const [selectedPostIndex, setSelectedPostIndex] = useState<number | null>(null);
  const [expandedQAs, setExpandedQAs] = useState<string[]>([]);
  const [isDetailsClosing, setIsDetailsClosing] = useState(false);

  // Fetch news data from API on page load
  const fetchNewsData = async () => {
    try {
      // Get current page URL as query parameter
      const currentUrl = encodeURIComponent(window.location.href);
      const apiUrl = `http://localhost:5173/news-api.json?url=${currentUrl}`;
      
      const response = await fetch(apiUrl);
      if (response.ok) {
        const data: NewsData = await response.json();
        setNewsData(data);
        setShouldShowWidget(true);
        setWidgetMode('summary'); // Show summary initially if data exists
      } else {
        throw new Error(`API request failed with status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error fetching news data:', error);
      setNewsData(null);
      setShouldShowWidget(false); // Don't show widget if data fetch fails
    } finally {
      setIsLoadingData(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchNewsData();
  }, []);



  // Callback functions for ForYouWidget and NewsDetailsDialog
  const handleTogglePost = (index: number) => {
    if (selectedPostIndex === index) {
      handleCloseDetails();
    } else {
      // Reset closing state when opening a new post
      setIsDetailsClosing(false);
      setSelectedPostIndex(index);
      setExpandedQAs(prevQAs => prevQAs.filter(qa => qa.startsWith(`${index}-`)));
    }
  };

  const handleToggleQA = (questionId: string) => {
    setExpandedQAs(prev => {
      if (prev.includes(questionId)) {
        return prev.filter(qa => qa !== questionId);
      } else {
        return [...prev, questionId];
      }
    });
  };

  const handleCloseDetails = () => {
    setIsDetailsClosing(true);
    setTimeout(() => {
      setSelectedPostIndex(null);
      setExpandedQAs([]);
      setIsDetailsClosing(false);
    }, 500); // Match the transition duration
  };

  const handleOpenPost = (url: string, e: React.MouseEvent) => {
    e.stopPropagation();
    window.open(url, '_blank');
  };

  return (
    <div className="relative">
      
      {/* Control Icons - only show if data exists and not loading */}
      {shouldShowWidget && !isLoadingData && (
        <div className="fixed bottom-12 right-12 sm:bottom-16 sm:right-16 z-50 flex flex-col gap-3 sm:gap-4 p-2 pt-4 rounded-full backdrop-blur-md bg-green-200/20 border border-gray-300/60 shadow">
            {/* AI Summary Button */}
            <div className="relative group">
              <button
                onClick={() => {
                  if (widgetMode === 'summary') {
                    setWidgetMode(null);
                  } else {
                    setWidgetMode('summary');
                  }
                }}
                className={`relative p-4 sm:p-5 rounded-full shadow-2xl transition-all duration-500 hover:scale-110
                           bg-gradient-to-br from-green-500 to-green-600 hover:shadow-[0_0_40px_rgba(34,197,94,1)]
                           text-white
                           pulse-glow float-animation
                           active:scale-95 group-hover:rotate-12 touch-target mobile-button`}
              >
                <AiSummaryIcon size={24} className="sm:w-7 sm:h-7" />
              </button>

              {/* Tooltip */}
              <div className="absolute right-full mr-3 top-1/2 -translate-y-1/2 invisible group-hover:visible
                              transition-opacity duration-0 pointer-events-none hidden sm:block">
                <div className="bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap
                                shadow-lg border border-gray-700">
                  AI Summary
                  <div className="absolute left-full top-1/2 -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
                </div>
              </div>
            </div>

            {/* Related Posts Button */}
            <div className="relative group">
              <button
                onClick={() => {
                  if (widgetMode === 'related') {
                    setWidgetMode(null);
                    handleCloseDetails();
                  } else {
                    setWidgetMode('related');
                    handleCloseDetails(); // Close any open details when opening
                  }
                }}
                className={`relative p-4 sm:p-5 rounded-full shadow-2xl transition-all duration-500 hover:scale-110
                           bg-gradient-to-br from-yellow-500 to-yellow-600 hover:shadow-[0_0_40px_rgba(251,191,36,1)]
                           text-white
                           pulse-glow float-animation
                           active:scale-95 group-hover:rotate-12 touch-target mobile-button`}
                style={{ animationDelay: '1s' }}
              >
                <ForYouIcon size={24} className="sm:w-7 sm:h-7 drop-shadow-lg" />
              </button>

              {/* Tooltip */}
              <div className="absolute right-full mr-3 top-1/2 -translate-y-1/2 invisible group-hover:visible
                              transition-opacity duration-0 pointer-events-none hidden sm:block">
                <div className="bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap
                                shadow-lg border border-gray-700">
                  For You
                  <div className="absolute left-full top-1/2 -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
                </div>
              </div>
            </div>
          </div>
      )}
      
      {/* NewsDetailsDialog - Large screens: side-by-side positioning */}
      {shouldShowWidget && widgetMode === 'related' && newsData && (selectedPostIndex !== null || isDetailsClosing) && (
        <div
          className={`fixed top-1/2 -translate-y-1/2 z-[10000] hidden md:block transition-all duration-500 ease-out ${
            selectedPostIndex !== null && !isDetailsClosing
              ? 'opacity-100 translate-x-0 scale-100'
              : 'opacity-0 -translate-x-8 scale-95 pointer-events-none'
          }`}
          style={{
            // Position to the left of NewsWidget with 26px gap
            right: 'calc(min(95vw, 580px) + 26px)',
            width: 'min(500px, calc(100vw - min(95vw, 400px) - 52px))', // Wider
            height: 'min(800px, 85vh)', // Taller
            transitionDelay: selectedPostIndex !== null && !isDetailsClosing ? '50ms' : '0ms'
          }}
        >
          <NewsDetailsDialog
            selectedPost={selectedPostIndex !== null ? newsData.related_posts[selectedPostIndex] : null}
            selectedPostIndex={selectedPostIndex}
            expandedQAs={expandedQAs}
            onClose={handleCloseDetails}
            onToggleQA={handleToggleQA}
            onOpenPost={handleOpenPost}
          />
        </div>
      )}

      {/* NewsDetailsDialog - Mobile screens: centered positioning */}
      {shouldShowWidget && widgetMode === 'related' && newsData && (selectedPostIndex !== null || isDetailsClosing) && (
        <div
          className={`fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-[10000] md:hidden transition-all duration-500 ease-out ${
            selectedPostIndex !== null && !isDetailsClosing
              ? 'opacity-100 scale-100'
              : 'opacity-0 scale-90 pointer-events-none'
          }`}
          style={{
            width: 'min(450px, 90vw)',
            height: 'min(600px, 80vh)',
            transitionDelay: selectedPostIndex !== null && !isDetailsClosing ? '50ms' : '0ms'
          }}
        >
          <NewsDetailsDialog
            selectedPost={selectedPostIndex !== null ? newsData.related_posts[selectedPostIndex] : null}
            selectedPostIndex={selectedPostIndex}
            expandedQAs={expandedQAs}
            onClose={handleCloseDetails}
            onToggleQA={handleToggleQA}
            onOpenPost={handleOpenPost}
          />
        </div>
      )}

      <NewsWidget
        mode={shouldShowWidget ? widgetMode : null}
        onClose={() => {
          setWidgetMode(null);
          // Close any open details when closing the For You widget
          if (widgetMode === 'related') {
            handleCloseDetails();
          }
        }}
        selectedPostIndex={selectedPostIndex}
        onTogglePost={handleTogglePost}
        onOpenPost={handleOpenPost}
        newsData={newsData}
      />
    </div>
  );
};

export default Index;
