import React, { useState } from 'react';
import { ChevronDown, ChevronUp, ExternalLink, X, Sparkles } from 'lucide-react';
// import { trackEvent } from '@/lib/ga4';

interface QAPair {
  question: string;
  answer: string;
}

interface RelatedPost {
  post_title: string;
  relevance_summary: string;
  qa_pairs: QAPair[];
  score: number;
  post_url: string;
}

interface NewsDetailsDialogProps {
  selectedPost: RelatedPost | null;
  selectedPostIndex: number | null;
  expandedQAs: string[];
  onClose: () => void;
  onToggleQA: (questionId: string) => void;
  onOpenPost: (url: string, e: React.MouseEvent) => void;
}

const NewsDetailsDialog: React.FC<NewsDetailsDialogProps> = ({
  selectedPost,
  selectedPostIndex,
  expandedQAs,
  onClose,
  onToggleQA,
  onOpenPost
}) => {
  const [showQA, setShowQA] = useState(false);
  const [isLoadingQA, setIsLoadingQA] = useState(false);
  const [containerHeight, setContainerHeight] = useState<'collapsed' | 'expanded'>('collapsed');
  const contentRef = React.useRef<HTMLDivElement>(null);

  // Reset Q&A state when selectedPost or selectedPostIndex changes
  React.useEffect(() => {
    setShowQA(false);
    setIsLoadingQA(false);
    setContainerHeight('collapsed');
  }, [selectedPost, selectedPostIndex]);

  // Animate height when Q&A is shown/hidden
  React.useEffect(() => {
    if (showQA && selectedPost?.qa_pairs?.length > 0) {
      setContainerHeight('expanded');
    } else {
      setContainerHeight('collapsed');
    }
  }, [showQA, selectedPost]);

  // If selectedPost is null (during closing animation), don't render content
  if (!selectedPost) {
    return <div className="w-full h-full bg-white/95 backdrop-blur-md border border-gray-400/90 rounded-2xl shadow-2xl overflow-hidden flex flex-col" />;
  }

  // Function to get random 3 questions from the available Q&A pairs
  const getRandomQuestions = (qaPairs: QAPair[], count: number = 3) => {
    if (qaPairs.length <= count) return qaPairs;

    const shuffled = [...qaPairs].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  };

  const handleShowQA = () => {
    setIsLoadingQA(true);
    setShowQA(true);

    // Simulate loading for 1-2 seconds randomly
    const loadingTime = Math.random() * 1000 + 1000; // 1-2 seconds
    setTimeout(() => {
      setIsLoadingQA(false);
    }, loadingTime);
  };

  if (!selectedPost || selectedPostIndex === null) {
    return null;
  }

  return (
    <div
      className="w-full bg-white/95 backdrop-blur-md border-2 border-gray-200 rounded-2xl shadow-md overflow-hidden flex flex-col animate-in fade-in duration-300 transition-all"
      style={{
        maxHeight: selectedPost.qa_pairs && selectedPost.qa_pairs.length > 0 && showQA
          ? (window.innerWidth < 640 ? '90vh' : '800px')
          : '1000px', // adjust as needed to fit summary/buttons content
        transition: 'max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1)'
      }}
    >
      {/* Details Header */}
      <div className="p-4 border-b border-gray-300/80 flex items-center justify-between bg-gradient-to-r from-green-50/60 to-gray-50/40">
        <h3 className="font-bold text-gray-700 text-lg line-clamp-2 flex-1 pr-4">
          {selectedPost.post_title}
        </h3>
        <button
          onClick={onClose}
          className="p-2 hover:bg-gray-200 rounded-lg transition-colors flex-shrink-0"
        >
          <X size={16} className="text-gray-500" />
        </button>
      </div>

      {/* Dialog Content */}
      {selectedPost.qa_pairs && selectedPost.qa_pairs.length > 0 && showQA ? (
        <div ref={contentRef} className="flex-1 overflow-y-auto">
          {/* Relevance Summary - Formatted like main post summary */}
          <div className="p-4 border-b border-gray-300/80">
            <h4 className="font-semibold text-gray-700 mb-4 text-sm">Why this is relevant:</h4>
            <div className="relative">
              <div className="bg-gradient-to-br from-white via-gray-50/20 to-white rounded-xl border border-gray-200/80 shadow-sm hover:shadow-md hover:border-gray-300/80 transition-all duration-300">
                <div className="p-4">
                  <div className="space-y-3">
                    {selectedPost.relevance_summary.split('\n').map((line, index) => {
                      const trimmedLine = line.trim();
                      if (!trimmedLine) return null;

                      return (
                        <div
                          key={index}
                          className="group slide-up"
                          style={{ animationDelay: `${index * 0.1}s` }}
                        >
                          <div className="flex items-start gap-3 p-2 rounded-lg hover:bg-green-50/40 transition-colors duration-200">
                            {/* Modern bullet point with gradient */}
                            <div className="flex-shrink-0 mt-1.5">
                              <div className="w-1.5 h-1.5 bg-gradient-to-r from-green-500 to-green-600 rounded-full group-hover:scale-125 group-hover:shadow-sm transition-all duration-200"></div>
                            </div>
                            {/* Content */}
                            <p className="text-gray-700 leading-relaxed text-xs sm:text-sm font-medium group-hover:text-gray-900 transition-colors duration-200">
                              {trimmedLine.replace(/^•\s*/, '')}
                            </p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
                {/* Modern accent line with gradient */}
                <div className="h-1.5 bg-gradient-to-r from-gray-200 via-green-500 to-gray-200 rounded-b-xl"></div>
              </div>
              {/* Modern floating elements */}
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-gradient-to-r from-green-500 to-green-600 rounded-full animate-pulse shadow-sm"></div>
              <div className="absolute -bottom-1 -left-1 w-2.5 h-2.5 bg-gradient-to-r from-green-300 to-gray-200 rounded-full animate-pulse shadow-sm" style={{ animationDelay: '1s' }}></div>
            </div>
          </div>

          {/* Action Buttons - Side by side layout */}
          <div className="p-4 border-b border-gray-300/80">
            <div className="flex flex-col sm:flex-row gap-3">
              {/* Read Full Article Button - Left side */}
              <div className="flex-1">
                <button
                  onClick={(e) => onOpenPost(selectedPost.post_url, e)}
                  className="flex items-center justify-center gap-2 w-full p-3 bg-gradient-to-r from-green-50 to-green-100/60 hover:from-green-100 hover:to-green-200/60 rounded-lg transition-all duration-200 group"
                >
                  <ExternalLink size={16} className="text-green-600 group-hover:text-green-700" />
                  <span className="text-green-700 font-medium text-sm group-hover:text-green-800">
                    Read Full Article
                  </span>
                </button>
              </div>

              {/* Need to Know More Button - Right side */}
              <div className="flex-1">
                <button
                  onClick={() => {
                    handleShowQA();
                    // trackEvent('button_click', {
                    //   button_name: 'generate_qna',
                    //   post_id: selectedPost.post_url
                    // });
                  }}
                  disabled={showQA}
                  className={`flex items-center justify-center gap-2 w-full p-3 rounded-lg transition-all duration-200 group ${
                    showQA
                      ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                      : 'bg-gradient-to-r from-blue-50 to-blue-100/60 hover:from-blue-100 hover:to-blue-200/60 text-blue-700 hover:text-blue-800'
                  }`}
                >
                  <Sparkles size={16} className={showQA ? '' : 'group-hover:animate-pulse'} />
                  <span className="font-medium text-sm">
                    Need to Know More?
                  </span>
                </button>
              </div>
            </div>
          </div>

          {/* Q&A Section - Redesigned with new title */}
          <div className="p-4">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-1 h-6 bg-gradient-to-b from-blue-500 to-blue-600 rounded-full"></div>
              <h4 className="font-bold text-gray-800 text-base">Need to Know More?</h4>
            </div>

            {isLoadingQA ? (
              // Loading animation
              <div className="text-center py-8">
                <div className="relative mx-auto mb-4 w-12 h-12">
                  <div className="absolute inset-0 rounded-full border-4 border-gray-100"></div>
                  <div className="absolute inset-0 rounded-full border-4 border-t-transparent border-blue-500 animate-spin"></div>
                </div>
                <p className="text-base font-medium text-gray-800 mb-2">Loading Questions...</p>
                <p className="text-sm text-blue-600">
                  Preparing insightful questions about this article
                </p>
              </div>
            ) : isLoadingQA ? (
              // Loading animation
              <div className="text-center py-8">
                <div className="relative mx-auto mb-4 w-12 h-12">
                  <div className="absolute inset-0 rounded-full border-4 border-gray-100"></div>
                  <div className="absolute inset-0 rounded-full border-4 border-t-transparent border-green-500 animate-spin"></div>
                </div>
                <p className="text-base font-medium text-gray-800 mb-2">Generating AI Questions...</p>
                <p className="text-sm text-green-600">
                  Our AI is analyzing the content to create insightful questions
                </p>
              </div>
            ) : (
              // Q&A list with random 3 questions - Responsive width
              <div className="space-y-4">
                {getRandomQuestions(selectedPost.qa_pairs).map((qa, qaIndex) => {
                  const questionId = `${selectedPostIndex}-${qaIndex}`;
                  const isExpanded = expandedQAs.includes(questionId);

                  return (
                    <div
                      key={qaIndex}
                      className={`w-full max-w-none sm:max-w-2xl mx-auto border-2 rounded-2xl overflow-hidden bg-gradient-to-br from-white to-gray-50/30 transition-all duration-300 ${
                        isExpanded
                          ? 'border-blue-500/60 shadow-lg ring-2 ring-blue-500/20'
                          : 'border-gray-300/60 hover:border-gray-400/80 hover:shadow-md'
                      }`}
                    >
                      <button
                        onClick={() => onToggleQA(questionId)}
                        className="w-full p-4 sm:p-5 text-left hover:bg-gradient-to-r hover:from-blue-50/30 hover:to-gray-50/30 transition-all duration-200 group"
                      >
                        <div className="flex items-start justify-between gap-3 sm:gap-4">
                          <div className="flex items-start gap-2 sm:gap-3 flex-1">
                            <div className="flex-shrink-0 mt-1">
                              <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full group-hover:scale-125 transition-all duration-200"></div>
                            </div>
                            <div className="flex-1">
                              <p className="font-semibold text-gray-800 text-sm sm:text-base leading-relaxed group-hover:text-blue-700 transition-colors">
                                {qa.question}
                              </p>
                            </div>
                          </div>
                          <div className="flex-shrink-0 mt-1">
                            <div className={`p-1 rounded-full transition-all duration-200 ${
                              isExpanded
                                ? 'bg-blue-100 text-blue-600'
                                : 'bg-gray-100 text-gray-500 group-hover:bg-blue-50 group-hover:text-blue-600'
                            }`}>
                              {isExpanded ? (
                                <ChevronUp size={14} />
                              ) : (
                                <ChevronDown size={14} />
                              )}
                            </div>
                          </div>
                        </div>
                      </button>

                      <div
                        className={`transition-all duration-500 ease-in-out overflow-hidden ${
                          isExpanded
                            ? 'max-h-96 opacity-100'
                            : 'max-h-0 opacity-0'
                        }`}
                      >
                        <div className="px-4 sm:px-5 pb-4 sm:pb-5 border-t border-gray-200/60 bg-gradient-to-br from-blue-50/20 to-gray-50/10">
                          <div className="pt-4 pl-4 sm:pl-5">
                            <div className="relative">
                              <div className="absolute left-0 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-500 to-blue-300 rounded-full"></div>
                              <div className="pl-3 sm:pl-4 text-sm sm:text-base text-gray-700 leading-relaxed whitespace-pre-line font-medium">
                                {qa.answer}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      ) : (
        <>
          {/* Relevance Summary - Formatted like main post summary */}
          <div className="p-4 border-b border-gray-300/80">
            <h4 className="font-semibold text-gray-700 mb-4 text-sm">Why this is relevant:</h4>
            <div className="relative">
              <div className="bg-gradient-to-br from-white via-gray-50/20 to-white rounded-xl border border-gray-200/80 shadow-sm hover:shadow-md hover:border-gray-300/80 transition-all duration-300">
                <div className="p-4">
                  <div className="space-y-3">
                    {selectedPost.relevance_summary.split('\n').map((line, index) => {
                      const trimmedLine = line.trim();
                      if (!trimmedLine) return null;

                      return (
                        <div
                          key={index}
                          className="group slide-up"
                          style={{ animationDelay: `${index * 0.1}s` }}
                        >
                          <div className="flex items-start gap-3 p-2 rounded-lg hover:bg-green-50/40 transition-colors duration-200">
                            {/* Modern bullet point with gradient */}
                            <div className="flex-shrink-0 mt-1.5">
                              <div className="w-1.5 h-1.5 bg-gradient-to-r from-green-500 to-green-600 rounded-full group-hover:scale-125 group-hover:shadow-sm transition-all duration-200"></div>
                            </div>
                            {/* Content */}
                            <p className="text-gray-700 leading-relaxed text-xs sm:text-sm font-medium group-hover:text-gray-900 transition-colors duration-200">
                              {trimmedLine.replace(/^•\s*/, '')}
                            </p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
                {/* Modern accent line with gradient */}
                <div className="h-1.5 bg-gradient-to-r from-gray-200 via-green-500 to-gray-200 rounded-b-xl"></div>
              </div>
              {/* Modern floating elements */}
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-gradient-to-r from-green-500 to-green-600 rounded-full animate-pulse shadow-sm"></div>
              <div className="absolute -bottom-1 -left-1 w-2.5 h-2.5 bg-gradient-to-r from-green-300 to-gray-200 rounded-full animate-pulse shadow-sm" style={{ animationDelay: '1s' }}></div>
            </div>
          </div>

          {/* Action Buttons - Side by side layout */}
          <div className="p-4 border-b border-gray-300/80">
            <div className="flex flex-col sm:flex-row gap-3">
              {/* Read Full Article Button - Left side */}
              <div className="flex-1">
                <button
                  onClick={(e) => onOpenPost(selectedPost.post_url, e)}
                  className="flex items-center justify-center gap-2 w-full p-3 bg-gradient-to-r from-green-50 to-green-100/60 hover:from-green-100 hover:to-green-200/60 rounded-lg transition-all duration-200 group"
                >
                  <ExternalLink size={16} className="text-green-600 group-hover:text-green-700" />
                  <span className="text-green-700 font-medium text-sm group-hover:text-green-800">
                    Read Full Article
                  </span>
                </button>
              </div>

              {/* Need to Know More Button - Right side */}
              <div className="flex-1">
                <button
                  onClick={() => {
                    handleShowQA();
                    // trackEvent('button_click', {
                    //   button_name: 'generate_qna',
                    //   post_id: selectedPost.post_url
                    // });
                  }}
                  disabled={showQA}
                  className={`flex items-center justify-center gap-2 w-full p-3 rounded-lg transition-all duration-200 group ${
                    showQA
                      ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                      : 'bg-gradient-to-r from-blue-50 to-blue-100/60 hover:from-blue-100 hover:to-blue-200/60 text-blue-700 hover:text-blue-800'
                  }`}
                >
                  <Sparkles size={16} className={showQA ? '' : 'group-hover:animate-pulse'} />
                  <span className="font-medium text-sm">
                    Need to Know More?
                  </span>
                </button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default NewsDetailsDialog;
