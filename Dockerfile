# Use Node.js 18 as the base image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json (if available)
COPY package*.json ./

RUN npm -v
# Install dependencies
RUN npm install


# Copy the rest of the application code
COPY . .

# Build the application
RUN npm run build

# Expose port 8501
EXPOSE 8501

# Start the development server on port 8501
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "8501"]
